/* style.css */

* { 
    margin: 0; 
    padding: 0; 
    box-sizing: border-box; 
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #3b3b6d;
    color: #f5f5f7;
    margin: 0;
    padding: 20px;
}

.container { max-width: 1200px; margin: 0 auto; }

.header { 
    text-align: center; 
    color: white; 
    margin-bottom: 30px; 
}

.header h1 { text-align: center; font-weight: 600; }

.header p { 
    opacity: 0.9; 
    font-size: 1.1rem; 
}
        
.filters-container, .controls, .summary-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-group { margin-bottom: 15px; }

.filter-group:last-child { /* Ta bort marginal på sista gruppen i en container */
    margin-bottom: 0; 
}

.filter-group h3 { margin-top: 0; font-size: 1.1em; }

        
.filter-buttons button, 
.category-buttons-grid { display: flex; flex-wrap: wrap; gap: 10px; }
#show-all-toggle { 
    padding: 8px 12px; 
    margin: 5px 3px; 
    border-radius: 8px; 
    border: 1px solid #ddd; 
    background-color: #f8f9fa; 
    cursor: pointer; 
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

button {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #f5f5f7;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}
button:hover { background-color: rgba(255, 255, 255, 0.2); }
button.active { background-color: #0071e3; border-color: #0071e3; font-weight: 600; }
button:disabled { opacity: 0.5; cursor: not-allowed; }

.load-button { width: 100%; padding: 15px; font-size: 1.2em; font-weight: bold; background-color: #30d158; border-color: #30d158; }
.load-button:hover { background-color: #3ae168; }

/* Layout för lagerfilter och visningsläge */
.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    gap: 20px;
}

.filter-left {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.filter-right {
    display: flex;
}

/* Se till att alla filterknappar har samma storlek som andra knappar */
.filter-left button,
#view-mode-toggle {
    padding: 10px 15px;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #f5f5f7;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin: 3px;
}

.filter-left button:hover,
#view-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.2);
}
.filter-buttons button.active,
#category-buttons button.active,
.filter-left button.active,
#view-mode-toggle.active {
    background-color: #0071e3;
    color: white;
    border-color: #0071e3;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.results { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
.category-headline, .subgroup-headline { grid-column: 1 / -1; color: white; }
.category-headline { margin: 40px 0 15px 0; font-size: 1.8em; border-bottom: 2px solid #0071e3; padding-bottom: 5px; }
.subgroup-headline { margin: 20px 0 10px 0; font-size: 1.3em; }

.product-card {
    background: #2c2c2c;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}
.product-card:hover { transform: translateY(-5px); box-shadow: 0 8px 20px rgba(0,0,0,0.4); }
.out-of-stock-card { opacity: 0.6; }

.product-title { font-weight: 600; font-size: 1.1em; }
.product-details { font-size: 0.9em; color: #ccc; line-height: 1.5; }
.detail-label { color: #888; }
.stock-info { display: flex; flex-direction: column; gap: 8px; }

.stock-item { display: flex; justify-content: space-between; align-items: center; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px; }
.stock-count { font-weight: bold; padding: 4px 8px; border-radius: 15px; font-size: 0.9em; }
.stock-count.in-stock { background-color: #30d158; color: black; }
.stock-count.empty { background-color: #ff453a; color: white; }

/* Summering & Tabeller */
.summary-content-box { font-size: 0.95em; }
.summary-details-list { display: grid; grid-template-columns: auto 1fr; gap: 8px 15px; }
.summary-details-list dt { font-weight: 600; color: #aaa; }
.summary-h4 { margin-top: 25px; margin-bottom: 10px; font-size: 1.1em; border-bottom: 1px solid #444; padding-bottom: 5px; }

.turnover-table, .distribution-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
.turnover-table th, .turnover-table td, .distribution-table th, .distribution-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #444; }
.turnover-table td:nth-child(2), .distribution-table td:nth-child(2) { color: #30d158; } /* Grön */
.turnover-table td:nth-child(3) { color: #ff453a; } /* Röd */

/* Modal för produktdetaljer */
.modal-backdrop {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background: rgba(0, 0, 0, 0.7); backdrop-filter: blur(5px);
    display: none; justify-content: center; align-items: center; z-index: 1000;
    opacity: 0; transition: opacity 0.3s ease;
}
.modal-backdrop.visible { opacity: 1; }

.modal-content {
    background: #1e1e3f; color: white; padding: 25px 35px;
    border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.2);
    width: 90%; max-width: 750px; max-height: 85vh;
    overflow-y: auto; position: relative;
    box-shadow: 0 10px 40px rgba(0,0,0,0.6);
}
.modal-close {
    position: absolute; top: 10px; right: 15px; background: none; border: none;
    font-size: 2.5rem; color: #aaa; cursor: pointer; transition: color 0.2s;
}
.modal-close:hover { color: white; }
.modal-h4 { margin-top: 25px; margin-bottom: 10px; font-size: 1.1em; color: #f5f5f7; border-bottom: 1px solid #444; padding-bottom: 5px;}
.stock-info-modal { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 8px 15px; margin-top: 15px;}
.spinner-container { text-align: center; padding: 40px; }
.spinner { /* ... din spinner-CSS ... */ }
.info-message, .error { text-align: center; padding: 50px; grid-column: 1 / -1; }

.filter-buttons button:hover:not(.active),
#category-buttons button:hover:not(.active),
.filter-left button:hover:not(.active),
#view-mode-toggle:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.2);
}
.store-selector { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 15px 0; }
.store-card { background: #333; padding: 15px; border-radius: 8px; cursor: pointer; border: 2px solid transparent; transition: all 0.2s; }
.store-card.selected { border-color: #0071e3; background: #3a3a5a; }
.store-name { font-weight: bold; }
.store-address { font-size: 0.9em; color: #ccc; }
.distance { font-size: 0.8em; color: #aaa; margin-top: 5px; }

.controls { 
    background: white; 
    border-radius: 15px; 
    padding: 25px; 
    margin-bottom: 30px; 
    box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
}

#location-status-text { 
    font-weight: 500; 
    margin-bottom:15px; 
    display:block; 
    text-align: center; 
    color: #555;
    min-height: 1.2em; /* För att undvika hopp när texten ändras */
}

.store-selector { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
    gap: 15px; 
    margin-bottom: 20px; 
}

.store-card { 
    padding: 15px; 
    border: 2px solid #e9ecef; 
    border-radius: 10px; 
    cursor: pointer; 
    transition: all 0.3s ease; 
    background: white; 
}

.store-card:hover { 
    border-color: #667eea; 
    transform: translateY(-2px); 
    box-shadow: 0 5px 15px rgba(0,0,0,0.08); 
}

.store-card.selected { 
    border-color: #667eea; 
    background: #f0f5ff; 
    font-weight: bold; 
}

.store-name { 
    font-weight: 600; 
    color: #2c3e50; 
    margin-bottom: 5px; 
}

.store-address { 
    color: #6c757d; 
    font-size: 0.9rem; 
}

.distance { 
    color: #667eea; 
    font-size: 0.8rem; 
    font-weight: 500; 
    margin-top: 5px; 
}

.load-button { 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
    color: white; 
    border: none; 
    padding: 15px 30px; 
    border-radius: 10px; 
    font-size: 1.1rem; 
    font-weight: 600; 
    cursor: pointer; 
    transition: transform 0.2s ease; 
    width: 100%; 
    margin-top:10px;
}

.load-button:hover { 
    transform: translateY(-2px); 
}

.load-button:disabled { 
    opacity: 0.6; 
    cursor: not-allowed; 
    transform: none; 
    background: #ccc; 
}

.results { 
    display: grid; 
    grid-template-columns: repeat(auto-fill, minmax(330px, 1fr)); 
    gap: 20px; 
}

.results h2 { /* Stil för kategorirubriker i resultatlistan */
    grid-column: 1 / -1; 
    color: white; 
    margin-top: 30px; 
    margin-bottom: 15px; 
    text-align: left; 
    padding-bottom: 5px; 
    border-bottom: 2px solid rgba(255,255,255,0.5); 
    font-size: 1.6rem;
}
.results h3 { /* Stil för sub-kategorirubriker */
    grid-column: 1 / -1;
    color: rgba(255,255,255,0.85);
    margin-top: 15px;
    margin-bottom: 8px;
    text-align: left;
    padding-left: 10px; /* Lite indrag för subrubrik */
    font-size: 1.2rem;
    font-weight: 500;
}


.product-card { 
    background: white; 
    border-radius: 15px; 
    padding: 20px; 
    box-shadow: 0 8px 25px rgba(0,0,0,0.08); 
    transition: all 0.3s ease; 
}

.product-card:hover { 
    transform: translateY(-5px); 
}

.product-card.out-of-stock-card { 
    opacity: 0.65; 
}

.product-title { 
    font-size: 1.1rem; 
    font-weight: 600; 
    color: #2c3e50; 
    margin-bottom: 5px; 
    line-height: 1.4; 
}

.product-details { 
    font-size:0.8em; 
    color:#444; 
    margin-bottom:10px; 
    line-height: 1.5;
}

.stock-info { 
    display: grid; 
    gap: 10px; 
    margin-top:10px; 
}

.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #d2d5d8;
    border-radius: 8px;
}

.store-info { 
    flex: 1; 
}

.store-name-small { 
    font-weight: 500; 
    color: #2c3e50; 
}

.store-city { 
    font-size: 0.85rem; 
    color: #6c757d; 
}

.stock-count { 
    background: #28a745; 
    color: white; 
    padding: 5px 12px; 
    border-radius: 20px; 
    font-weight: 600; 
    font-size: 0.9rem; 
}

.stock-count.low { /* För framtida bruk om du vill indikera få i lager */
    background: #ffc107;
    color: #333;
}

.stock-count.empty {
    background: #adb5bd;
    color: #fff;
}

/* Nya stilar för orange varning vid lågt lager */
.stock-item.low-stock-item {
    background-color: rgba(255, 149, 0, 0.15);
    border-left: 4px solid #ff9500;
}

.stock-count.low-stock {
    background: #ff9500;
    color: white;
    font-weight: bold;
}

.stock-item.out-of-stock-item {
    opacity: 0.6;
}

/* === PROFESSIONELL KONTROLLPANEL === */
.control-panel {
    background: rgba(45, 45, 75, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Söksektion */
.search-section {
    margin-bottom: 25px;
}

.search-wrapper {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

#search-input {
    width: 100%;
    padding: 15px 20px;
    font-size: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.1);
    color: #f5f5f7;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

#search-input:focus {
    outline: none;
    border-color: #0071e3;
    box-shadow: 0 0 0 4px rgba(0, 113, 227, 0.2), 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
}

#search-input::placeholder {
    color: rgba(245, 245, 247, 0.6);
}

#clear-search {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: #8e8e93;
    color: white;
    border: none;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#clear-search:hover {
    background: #ff3b30;
    transform: translateY(-50%) scale(1.1);
}

/* Huvudkontroller */
.main-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 25px;
}

.control-group {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-title {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #f5f5f7;
    text-align: center;
}

.button-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

/* Alla knappar i button-grid */
.button-grid button {
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #f5f5f7;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: none;
}

.button-grid button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.button-grid button.active {
    background: #0071e3;
    color: white;
    border-color: #0071e3;
    box-shadow: 0 2px 8px rgba(0, 113, 227, 0.3);
}

/* Avancerade filter */
.advanced-filters {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: end;
    gap: 20px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-item-main {
    flex: 1;
}

.filter-item-compact {
    flex: 0 0 auto;
    align-self: flex-end;
}

.filter-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #c7c7cc;
    margin: 0;
}

.filter-buttons-compact {
    display: flex;
    gap: 5px;
}

.filter-buttons-compact button {
    flex: 1;
    padding: 10px 14px;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #f5f5f7;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: none;
}

.filter-buttons-compact button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.filter-buttons-compact button.active {
    background: #0071e3;
    color: white;
    border-color: #0071e3;
    box-shadow: 0 2px 6px rgba(0, 113, 227, 0.3);
}

.sort-select {
    padding: 12px 15px;
    border: 2px solid #d1d1d6;
    border-radius: 8px;
    background: white;
    color: #1d1d1f;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sort-select:hover {
    border-color: #0071e3;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.sort-select:focus {
    outline: none;
    border-color: #0071e3;
    box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
}

.view-toggle {
    padding: 12px 16px;
    border: 2px solid #d1d1d6;
    border-radius: 8px;
    background: white;
    color: #1d1d1f;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-toggle:hover {
    background: #f5f5f7;
    border-color: #0071e3;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.view-toggle.active {
    background: #0071e3;
    color: white;
    border-color: #0071e3;
    box-shadow: 0 2px 6px rgba(0, 113, 227, 0.3);
}

/* Kompakt view-toggle knapp */
.view-toggle-compact {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #f5f5f7;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 90px;
}

.view-toggle-compact:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.view-toggle-compact.active,
#view-mode-toggle.active {
    background: #0071e3 !important;
    color: white !important;
    border-color: #0071e3 !important;
    box-shadow: 0 2px 6px rgba(0, 113, 227, 0.3) !important;
}

/* Responsiv design */
@media (max-width: 768px) {
    .main-controls {
        grid-template-columns: 1fr;
        gap: 20px;
    }



    .button-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .control-panel {
        padding: 20px;
        margin-bottom: 20px;
    }
}

.loading { 
    text-align: center; 
    padding: 50px; 
    color: white; 
    font-size: 1.2rem; 
}

.spinner { 
    width: 40px; 
    height: 40px; 
    border: 4px solid rgba(255,255,255,0.3); 
    border-top: 4px solid white; 
    border-radius: 50%; 
    animation: spin 1s linear infinite; 
    margin: 0 auto 20px; 
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

.error { 
    background: #dc3545; 
    color: white; 
    padding: 15px; 
    border-radius: 10px; 
    margin: 20px 0; 
    text-align: center; 
}

/* --- Stilar för Summeringssektionen --- */
#summary-section {
    background: rgba(255,255,255,0.9);
    color: #333;
    padding: 20px 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    border: 2px solid rgba(102, 126, 234, 0.3);
}

#summary-section h3 { /* Rubriken "Summering för aktuellt urval" */
    font-size: 1.3rem; 
    color: #2c3e50;   
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #667eea; 
    text-align: left;
}

#summary-section h4 { /* Rubriken "Lagerfördelning per butik" */
    font-size: 1.1rem;
    color: #34495e;
    margin-top: 40px;
    margin-bottom: 10px;
}

.summary-details-list { /* Ny klass för DL-listan i summeringen */
    display: grid;
    grid-template-columns: auto 1fr; 
    gap: 8px 15px; 
    font-size: 0.95em;
    margin-bottom: 20px; 
}

.summary-details-list dt {
    font-weight: 600; 
    color: #555;
    text-align: left; 
}

.summary-details-list dd {
    margin-left: 0;
    text-align: left; 
    font-weight: 500;
}

#summary-section table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 0.9em; 
}

#summary-section th, 
#summary-section td {
    text-align: left;
    padding: 10px 8px; 
    border-bottom: 1px solid #e0e0e0; 
}

#summary-section th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

/* Justera varje kolumnrubrik och cell */
#summary-section th:nth-child(2),
#summary-section td:nth-child(2) { /* Enheter */
  text-align: right;
  white-space: nowrap;
}

#summary-section th:nth-child(3),
#summary-section td:nth-child(3) { /* Andel */
  text-align: right;
  white-space: nowrap;
}

#summary-section th:nth-child(4),
#summary-section td:nth-child(4) { /* Värde */
  text-align: right;
  white-space: nowrap;
}

/* Lite extra färg på hover */
#summary-section tbody tr:hover {
  background-color: #f1f3f5;
}

#summary-section tbody tr:hover {
    background-color: #f1f3f5; 
}

#summary-content p { 
    margin-top: 15px;
    font-size: 0.95em;
    color: #444;
}
/* --- Slut på Summeringsstilar --- */


.footer-credit { 
    text-align: center; 
    color: rgba(255,255,255,0.7); 
    margin-top: 40px; 
    font-size: 0.9rem; 
}

@media (max-width: 768px) {
    .header h1 { font-size: 2rem; }
    .store-selector, .results { grid-template-columns: 1fr; }

    /* Responsiv layout för filter-knappar på mobil */
    .filter-row {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .filter-item-compact {
        align-self: stretch;
    }

    .filter-left {
        justify-content: center;
    }

    .filter-right {
        justify-content: center;
    }
}
.header {
  position: relative;
}
.switch-advanced {
  position: absolute;
  top: 8px;   /* eller t.ex. 32px, beroende på din header-höjd */
  right: 24px;
  display: flex;
  align-items: center;
  z-index: 10;
  cursor: pointer;
}
/* Gör produktkorten klickbara */
.product-card {
    /* ... dina befintliga stilar ... */
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

/* Modal-styling */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* En klass för att visa modalen med fade-in */
.modal-backdrop.visible {
    opacity: 1;
}

.modal-content {
    background: #2c2c2c;
    color: white;
    padding: 25px 35px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 90%;
    max-width: 750px;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 10px 40px rgba(0,0,0,0.6);
}

.modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 2.5rem;
    font-weight: 300;
    line-height: 1;
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s ease;
}
.modal-close:hover {
    color: white;
}

.modal-content h2 {
    margin-top: 0;
    border-bottom: 2px solid #0071e3;
    padding-bottom: 10px;
}

.details-sub {
    color: #ccc;
    margin: 5px 0;
}

.modal-h4 {
    margin-top: 25px;
    margin-bottom: 10px;
    font-size: 1.1em;
    color: #f5f5f7;
    border-bottom: 1px solid #444;
    padding-bottom: 5px;
}

.stock-info-modal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px 15px;
    margin-top: 15px;
}

/* Tabell-styling för lageromsättning */
.turnover-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 0.95em;
}

.turnover-table th, .turnover-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #444;
}

.turnover-table th {
    color: #f5f5f7;
    font-weight: 600;
}

.turnover-table td:nth-child(2) {
    color: #30d158; /* Grön för inkommet */
}

.turnover-table td:nth-child(3) {
    color: #ff453a; /* Röd för sålt */
}

.turnover-table tr:last-child td {
    border-bottom: none;
}