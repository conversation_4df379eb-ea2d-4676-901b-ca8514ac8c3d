<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Lagerstatus</title>
    <link rel="stylesheet" href="style.css?v=3.0.0">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Power Lagerstatus</h1>
            <p>Intern portal för lagerkoll</p>
        </div>

        <!-- Professionell kontrollpanel -->
        <div class="control-panel">
            <!-- Söksektion -->
            <div class="search-section">
                <div class="search-wrapper">
                    <input type="text" id="search-input" placeholder="Sök på produktnamn, EAN, SKU...">
                    <button id="clear-search" onclick="clearSearch()" style="display: none;">×</button>
                </div>
            </div>

            <!-- Huvudkontroller i grid -->
            <div class="main-controls">
                <!-- Kategorier -->
                <div class="control-group">
                    <h4 class="control-title">Produktkategorier</h4>
                    <div id="category-buttons" class="button-grid">
                        <!-- Kategoriknappar genereras här av script.js -->
                    </div>
                </div>

                <!-- Butiker -->
                <div class="control-group">
                    <h4 class="control-title">Välj butiker</h4>
                    <div class="filter-buttons button-grid">
                        <button id="btn-closest" onclick="setStoreFilter('closest')">Min närmaste</button>
                        <button id="btn-nearby" onclick="setStoreFilter('nearby')">Närliggande</button>
                        <button id="btn-all_stores" onclick="setStoreFilter('all_stores')">Alla butiker</button>
                        <button id="btn-user_selected" onclick="setStoreFilter('user_selected')">Anpassat urval</button>
                    </div>
                </div>
            </div>

            <!-- Avancerade filter -->
            <div class="advanced-filters">
                <div class="filter-row">
                    <div class="filter-item">
                        <label class="filter-label">Lagerfilter</label>
                        <div class="filter-buttons-compact">
                            <button id="btn-stock-in_stock" onclick="setStockStatusFilter('in_stock')" class="active">I lager</button>
                            <button id="btn-stock-all" onclick="setStockStatusFilter('all')">Alla</button>
                            <button id="btn-stock-out_of_stock" onclick="setStockStatusFilter('out_of_stock')">Slut</button>
                        </div>
                    </div>
<!--
                    <div class="filter-item">
                        <label class="filter-label">Sortering</label>
                        <select id="sort-dropdown" onchange="setSortOption(this.value)" class="sort-select">
                            <option value="stock_desc">Mest lager först</option>
                            <option value="stock_asc">Minst lager först</option>
                            <option value="name_asc">Namn A-Ö</option>
                            <option value="name_desc">Namn Ö-A</option>
                            <option value="price_desc">Högsta pris först</option>
                            <option value="price_asc">Lägsta pris först</option>
                        </select>
                    </div>
-->
                    <div class="filter-item">
                        <label class="filter-label">Visning</label>
                        <button id="view-mode-toggle" onclick="toggleDisplayMode()" class="view-toggle">Kompakt vy</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <div id="location-status-text" style="text-align:center; margin-bottom:15px; color: #555; font-weight:500;">Söker din position...</div>

            <div class="store-selector" id="store-selector" style="display:none;">
                <!-- Butiker läggs till här dynamiskt av script.js -->
            </div>

            <button class="load-button" id="load-button" onclick="loadStockData()">
                Hämta lagerstatus
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div> Hämtar lagerstatus...
        </div>
        <div id="error" class="error" style="display: none;"></div>

        <div id="summary-section" class="filters-container" style="margin-bottom: 20px; display: none;">
            <div class="filter-group">
                <h3>Summering för aktuellt urval</h3>
                <div id="summary-content">
                    <!-- Summeringsinformation genereras här av script.js -->
                </div>
            </div>
        </div>

        <div id="results" class="results">
            <!-- Produktkort genereras här av script.js -->
        </div>
        
        <div class="footer-credit">
            <p>Skapad av SiR Charles</p>
        </div>
    </div>

    <script src="script.js?v=3.0.0"></script> <!-- Uppdaterad version med nya funktioner -->
</body>
</html>